# ResourceFileService Class Documentation

## Overview
The `ResourceFileService` class provides functionality to read JSON files from the `Resources` folder in the ThongBaoLuuTru.ConsoleApp project. It supports both reading JSON as strings and deserializing JSON to strongly-typed objects.

## Features
- Read JSON files from the Resources folder
- Deserialize JSON to strongly-typed objects
- Check if JSON files exist
- Automatic .json extension handling
- Comprehensive error handling
- Case-insensitive JSON property matching

## Setup

### 1. Dependencies
The FileHelper uses `System.Text.Json` for JSON serialization/deserialization. This package has been added to the project.

### 2. Dependency Injection
The ResourceFileService is registered in the DI container in `Program.cs`:

```csharp
.AddSingleton<IFileHelper, ResourceFileService>()
```

### 3. Resources Folder
JSON files should be placed in the `Resources` folder within the project. The folder structure should be:
```
ThongBaoLuuTru.ConsoleApp/
├── Resources/
│   ├── sample-config.json
│   ├── settings.json
│   └── other-config.json
└── ...
```

## Usage Examples

### Basic Usage with Dependency Injection
```csharp
// In your service or class constructor
public class MyService
{
    private readonly IFileHelper _fileHelper;
    
    public MyService(IFileHelper fileHelper)
    {
        _fileHelper = fileHelper;
    }
    
    public async Task LoadConfiguration()
    {
        var config = await _fileHelper.ReadJsonFileAsync<AppConfig>("sample-config");
        // Use config...
    }
}
```

### Direct Usage
```csharp
var fileHelper = new ResourceFileService();

// Check if file exists
bool exists = fileHelper.JsonFileExists("sample-config");

// Read JSON as string
string jsonContent = await fileHelper.ReadJsonFileAsStringAsync("sample-config");

// Read and deserialize JSON
var config = await fileHelper.ReadJsonFileAsync<AppConfig>("sample-config");
```

## Interface Methods

### `ReadJsonFileAsync<T>(string fileName)`
Reads a JSON file and deserializes it to the specified type.

**Parameters:**
- `fileName`: Name of the JSON file (with or without .json extension)

**Returns:** 
- `Task<T?>`: The deserialized object or null if deserialization fails

**Example:**
```csharp
var config = await fileHelper.ReadJsonFileAsync<AppConfig>("config");
```

### `ReadJsonFileAsStringAsync(string fileName)`
Reads a JSON file as a raw string.

**Parameters:**
- `fileName`: Name of the JSON file (with or without .json extension)

**Returns:** 
- `Task<string>`: The JSON content as a string

**Example:**
```csharp
string json = await fileHelper.ReadJsonFileAsStringAsync("config");
```

### `JsonFileExists(string fileName)`
Checks if a JSON file exists in the Resources folder.

**Parameters:**
- `fileName`: Name of the JSON file (with or without .json extension)

**Returns:** 
- `bool`: True if the file exists, false otherwise

**Example:**
```csharp
if (fileHelper.JsonFileExists("config"))
{
    // File exists, proceed with reading
}
```

## Sample Configuration

### JSON File (sample-config.json)
```json
{
  "appName": "ThongBaoLuuTru",
  "version": "1.0.0",
  "settings": {
    "timeout": 30000,
    "retryCount": 3,
    "enableLogging": true
  },
  "urls": {
    "landingUrl": "https://dichvucong.bocongan.gov.vn/",
    "formUrl": "https://dichvucong.bocongan.gov.vn/bocongan/bothutuc/tthc?matt=26346"
  },
  "credentials": {
    "username": "",
    "password": ""
  }
}
```

### Corresponding DTO Classes
```csharp
public class AppConfig
{
    public string AppName { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public Settings Settings { get; set; } = new();
    public Urls Urls { get; set; } = new();
    public Credentials Credentials { get; set; } = new();
}

public class Settings
{
    public int Timeout { get; set; }
    public int RetryCount { get; set; }
    public bool EnableLogging { get; set; }
}

public class Urls
{
    public string LandingUrl { get; set; } = string.Empty;
    public string FormUrl { get; set; } = string.Empty;
}

public class Credentials
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}
```

## Error Handling

The ResourceFileService provides comprehensive error handling:

- **FileNotFoundException**: Thrown when the specified JSON file doesn't exist
- **InvalidOperationException**: Thrown when JSON deserialization fails
- **IOException**: Thrown when file reading fails
- **ArgumentException**: Thrown when fileName is null or empty

## Best Practices

1. **Always use try-catch blocks** when calling ResourceFileService methods
2. **Check file existence** before reading if the file might not exist
3. **Use strongly-typed DTOs** for better type safety
4. **Place JSON files in the Resources folder** for proper organization
5. **Use dependency injection** when possible for better testability

## Testing

The project includes a `TestFileHelper` class that demonstrates all ResourceFileService functionality. Run the application to see the tests in action.
