﻿// See https://aka.ms/new-console-template for more information
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using PuppeteerSharp;
using ThongBaoLuuTru.ConsoleApp.Services;
using ThongBaoLuuTru.ConsoleApp.Services.Interfaces;
using ThongBaoLuuTru.ConsoleApp.Dtos;
class Program
{
    const string LandingUrl = "https://dichvucong.bocongan.gov.vn/";
    const string formUrl = "https://dichvucong.bocongan.gov.vn/bocongan/bothutuc/tthc?matt=26346";
    static async Task Main(string[] args)
    {
        Console.WriteLine("Get browser!");
        await new BrowserFetcher().DownloadAsync();

        // Run FileHelper tests
        await ThongBaoLuuTru.ConsoleApp.TestFileHelper.RunTests();

        // Configure services
        var serviceProvider = new ServiceCollection()
            .AddLogging(config =>
            {
                config.AddSimpleConsole(options =>
                {
                    options.TimestampFormat = "[yyyy-MM-dd HH:mm:ss] ";
                    options.IncludeScopes = true;
                    options.ColorBehavior = LoggerColorBehavior.Enabled;
                });
                config.SetMinimumLevel(LogLevel.Debug);
            })
            .AddSingleton<IPuppeteerService, PuppeteerService>()
            .AddSingleton<IFileHelper, FileHelper>()
            .BuildServiceProvider();

        // Demonstrate FileHelper usage
        var fileHelper = serviceProvider.GetRequiredService<IFileHelper>();

        try
        {
            // Check if sample config file exists
            if (fileHelper.JsonFileExists("sample-config"))
            {
                Console.WriteLine("Reading sample configuration from JSON file...");
                var config = await fileHelper.ReadJsonFileAsync<AppConfig>("sample-config");

                if (config != null)
                {
                    Console.WriteLine($"App: {config.AppName} v{config.Version}");
                    Console.WriteLine($"Timeout: {config.Settings.Timeout}ms");
                    Console.WriteLine($"Landing URL: {config.Urls.LandingUrl}");
                }
            }
            else
            {
                Console.WriteLine("Sample config file not found in Resources folder.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error reading JSON file: {ex.Message}");
        }

        // Resolve and use the service
        var puppeteerService = serviceProvider.GetRequiredService<IPuppeteerService>();
        await puppeteerService.LaunchBrowser();
        var loginResult = await puppeteerService.LoginPortal(LandingUrl);

        if (loginResult)
        {
            Console.WriteLine("Login success. Navigate to input form");
            await puppeteerService.ThongBaoLuuTru(formUrl);

        }


        Console.WriteLine("Done. Press any key to exit...");
        Console.ReadKey();

    }
}
