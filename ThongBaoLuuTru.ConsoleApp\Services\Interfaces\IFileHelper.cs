namespace ThongBaoLuuTru.ConsoleApp.Services.Interfaces
{
    public interface IFileHelper
    {
        /// <summary>
        /// Reads a JSON file from the Resources folder and deserializes it to the specified type
        /// </summary>
        /// <typeparam name="T">The type to deserialize the JSON to</typeparam>
        /// <param name="fileName">The name of the JSON file (with or without .json extension)</param>
        /// <returns>The deserialized object of type T</returns>
        Task<T?> ReadJsonFileAsync<T>(string fileName) where T : class;

        /// <summary>
        /// Reads a JSON file from the Resources folder as a string
        /// </summary>
        /// <param name="fileName">The name of the JSON file (with or without .json extension)</param>
        /// <returns>The JSON content as a string</returns>
        Task<string> ReadJsonFileAsStringAsync(string fileName);

        /// <summary>
        /// Checks if a JSON file exists in the Resources folder
        /// </summary>
        /// <param name="fileName">The name of the JSON file (with or without .json extension)</param>
        /// <returns>True if the file exists, false otherwise</returns>
        bool JsonFileExists(string fileName);
    }
}
