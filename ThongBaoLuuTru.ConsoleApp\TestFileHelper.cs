using ThongBaoLuuTru.ConsoleApp.Services;
using ThongBaoLuuTru.ConsoleApp.Dtos;

namespace ThongBaoLuuTru.ConsoleApp
{
    public static class TestFileHelper
    {
        public static async Task RunTests()
        {
            Console.WriteLine("=== FileHelper Tests ===");
            
            var fileHelper = new FileHelper();
            
            // Test 1: Check if file exists
            Console.WriteLine("\n1. Testing file existence check:");
            bool exists = fileHelper.JsonFileExists("sample-config");
            Console.WriteLine($"   sample-config.json exists: {exists}");
            
            if (!exists)
            {
                Console.WriteLine("   Creating sample config file for testing...");
                await CreateSampleConfigFile();
                exists = fileHelper.JsonFileExists("sample-config");
                Console.WriteLine($"   sample-config.json exists after creation: {exists}");
            }
            
            // Test 2: Read JSON as string
            Console.WriteLine("\n2. Testing read JSON as string:");
            try
            {
                string jsonContent = await fileHelper.ReadJsonFileAsStringAsync("sample-config");
                Console.WriteLine($"   JSON content length: {jsonContent.Length} characters");
                Console.WriteLine($"   First 100 characters: {jsonContent.Substring(0, Math.Min(100, jsonContent.Length))}...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error: {ex.Message}");
            }
            
            // Test 3: Read and deserialize JSON
            Console.WriteLine("\n3. Testing JSON deserialization:");
            try
            {
                var config = await fileHelper.ReadJsonFileAsync<AppConfig>("sample-config");
                if (config != null)
                {
                    Console.WriteLine($"   App Name: {config.AppName}");
                    Console.WriteLine($"   Version: {config.Version}");
                    Console.WriteLine($"   Timeout: {config.Settings.Timeout}ms");
                    Console.WriteLine($"   Retry Count: {config.Settings.RetryCount}");
                    Console.WriteLine($"   Enable Logging: {config.Settings.EnableLogging}");
                    Console.WriteLine($"   Landing URL: {config.Urls.LandingUrl}");
                    Console.WriteLine($"   Form URL: {config.Urls.FormUrl}");
                }
                else
                {
                    Console.WriteLine("   Config is null");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Error: {ex.Message}");
            }
            
            // Test 4: Test with non-existent file
            Console.WriteLine("\n4. Testing with non-existent file:");
            try
            {
                bool nonExistentExists = fileHelper.JsonFileExists("non-existent-file");
                Console.WriteLine($"   non-existent-file.json exists: {nonExistentExists}");
                
                var nonExistentConfig = await fileHelper.ReadJsonFileAsync<AppConfig>("non-existent-file");
                Console.WriteLine($"   This should not be reached");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Expected error: {ex.Message}");
            }
            
            Console.WriteLine("\n=== FileHelper Tests Completed ===\n");
        }
        
        private static async Task CreateSampleConfigFile()
        {
            var resourcesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources");
            Directory.CreateDirectory(resourcesPath);
            
            var sampleConfig = new
            {
                appName = "ThongBaoLuuTru",
                version = "1.0.0",
                settings = new
                {
                    timeout = 30000,
                    retryCount = 3,
                    enableLogging = true
                },
                urls = new
                {
                    landingUrl = "https://dichvucong.bocongan.gov.vn/",
                    formUrl = "https://dichvucong.bocongan.gov.vn/bocongan/bothutuc/tthc?matt=26346"
                },
                credentials = new
                {
                    username = "",
                    password = ""
                }
            };
            
            var json = System.Text.Json.JsonSerializer.Serialize(sampleConfig, new System.Text.Json.JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
            
            await File.WriteAllTextAsync(Path.Combine(resourcesPath, "sample-config.json"), json);
        }
    }
}
