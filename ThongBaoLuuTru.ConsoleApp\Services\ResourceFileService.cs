using System.Text.Json;
using ThongBaoLuuTru.ConsoleApp.Services.Interfaces;

namespace ThongBaoLuuTru.ConsoleApp.Services
{
    public class ResourceFileService : IFileHelper
    {
        private readonly string _resourcesPath;

        public ResourceFileService()
        {
            // Get the base directory of the application and combine with Resources folder
            _resourcesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources");
            
            // Ensure the Resources directory exists
            if (!Directory.Exists(_resourcesPath))
            {
                Directory.CreateDirectory(_resourcesPath);
            }
        }

        /// <summary>
        /// Reads a JSON file from the Resources folder and deserializes it to the specified type
        /// </summary>
        /// <typeparam name="T">The type to deserialize the JSON to</typeparam>
        /// <param name="fileName">The name of the JSON file (with or without .json extension)</param>
        /// <returns>The deserialized object of type T</returns>
        public async Task<T?> ReadJsonFileAsync<T>(string fileName) where T : class
        {
            try
            {
                var jsonContent = await ReadJsonFileAsStringAsync(fileName);
                
                if (string.IsNullOrEmpty(jsonContent))
                {
                    return null;
                }

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true
                };

                return JsonSerializer.Deserialize<T>(jsonContent, options);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to deserialize JSON file '{fileName}' to type '{typeof(T).Name}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reads a JSON file from the Resources folder as a string
        /// </summary>
        /// <param name="fileName">The name of the JSON file (with or without .json extension)</param>
        /// <returns>The JSON content as a string</returns>
        public async Task<string> ReadJsonFileAsStringAsync(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                throw new ArgumentException("File name cannot be null or empty.", nameof(fileName));
            }

            // Ensure the file has .json extension
            if (!fileName.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
            {
                fileName += ".json";
            }

            var filePath = Path.Combine(_resourcesPath, fileName);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"JSON file '{fileName}' not found in Resources folder. Expected path: {filePath}");
            }

            try
            {
                return await File.ReadAllTextAsync(filePath);
            }
            catch (Exception ex)
            {
                throw new IOException($"Failed to read JSON file '{fileName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Checks if a JSON file exists in the Resources folder
        /// </summary>
        /// <param name="fileName">The name of the JSON file (with or without .json extension)</param>
        /// <returns>True if the file exists, false otherwise</returns>
        public bool JsonFileExists(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
            {
                return false;
            }

            // Ensure the file has .json extension
            if (!fileName.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
            {
                fileName += ".json";
            }

            var filePath = Path.Combine(_resourcesPath, fileName);
            return File.Exists(filePath);
        }
    }
}
