﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PuppeteerExtraSharp.Plugins.ExtraStealth;
using PuppeteerExtraSharp;
using PuppeteerSharp;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ThongBaoLuuTru.ApiService.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ThongBaoLuuTru : ControllerBase
    {
        private readonly ILogger<ThongBaoLuuTru> _logger;
        private IBrowser _browser;
        private IPage _page;


        public ThongBaoLuuTru(ILogger<ThongBaoLuuTru> logger)
        {
            _logger = logger;
        }

        [HttpGet(Name = "thong_bao")]
        public async Task ThongBao()
        {
            var options = new LaunchOptions
            {
                Headless = false, // Set to true if you want headless mode
                //ExecutablePath = @"C:\Program Files\Google\Chrome\Application\chrome.exe", // Update this path
                Args = new[] { "--no-sandbox", "--disable-gpu" },
                DefaultViewport = null
            };

            var extra = new PuppeteerExtra();

            // Add stealth plugin
            extra.Use(new StealthPlugin());

            // Download browser if not already downloaded
            await new BrowserFetcher().DownloadAsync();

            // Launch browser
            var _browser = await extra.LaunchAsync(options);

            var _page = await _browser.NewPageAsync();

            try
            {
                // Navigate to the login page
                await _page.GoToAsync("https://dichvucong.bocongan.gov.vn/", WaitUntilNavigation.Networkidle2); // Replace with your URL

                // Wait for the button to appear
                var loginXPath = "//a[@role='button' and normalize-space(text())='Đăng nhập']";

                await _page.WaitForXPathAsync(loginXPath, new WaitForSelectorOptions { Visible = true});
                
                await _page.WaitForTimeoutAsync(3000);

                Console.WriteLine("Page loaded. Trigger login button");

                // Get the element and click it
                var elements = await _page.XPathAsync(loginXPath);
                if (elements.Length > 0)
                {
                    await elements[0].ClickAsync();
                }

                await _page.WaitForTimeoutAsync(3000);

                // Trigger login by VNeID
                var loginByVneIdXpath = "//div[@class='login-IDP' and @id='icon-2' and @title='Tài khoản Định danh điện tử cấp bởi Bộ Công an dành cho Công dân']";
                var loginElements = await _page.XPathAsync(loginByVneIdXpath);
                if (loginElements.Length > 0)
                {
                    await loginElements[0].ClickAsync();
                }



                // Wait for login with multiple detection methods
                //var loginResult = await WaitForLoginWithMultipleMethodsAsync(10);

                //if (loginResult)
                //{
                //    Console.WriteLine("Login successful!");
                //}
                //else
                //{
                //    Console.WriteLine("Login timeout or failed.");
                //}

                Console.WriteLine("Login successful!");

                // Continue with your automation after login
                // ...

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        private async Task<bool> WaitForLoginWithMultipleMethodsAsync(int timeoutMinutes)
        {
            var timeout = TimeSpan.FromMinutes(timeoutMinutes);
            var cts = new CancellationTokenSource(timeout);

            // Create multiple detection tasks
            var tasks = new[]
            {
                WaitForUrlChangeTask(cts.Token),
            };

            try
            {
                // Wait for any of the detection methods to succeed
                await Task.WhenAny(tasks);
                return true;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
        }

        private async Task WaitForUrlChangeTask(CancellationToken cancellationToken)
        {
            var initialUrl = _page.Url;

            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);

                if (_page.Url != initialUrl)
                {
                    Console.WriteLine($"URL changed to: {_page.Url}");
                    return;
                }
            }
        }

    }
}
