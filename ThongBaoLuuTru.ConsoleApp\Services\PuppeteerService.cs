﻿using PuppeteerExtraSharp.Plugins.ExtraStealth;
using PuppeteerExtraSharp;
using PuppeteerSharp;
using ThongBaoLuuTru.ConsoleApp.Services.Interfaces;
using Microsoft.Extensions.Logging;
using static System.Net.Mime.MediaTypeNames;

namespace ThongBaoLuuTru.ConsoleApp.Services
{
    public class PuppeteerService : IPuppeteerService
    {
        private readonly ILogger<PuppeteerService> _logger;
        private readonly LaunchOptions _options;
        private readonly PuppeteerExtra _extra;
        private IBrowser _browser;
        private IPage _page;
        public PuppeteerService(ILogger<PuppeteerService> logger)
        {
            _options = new LaunchOptions
            {
                Headless = false, // Set to true if you want headless mode
                //ExecutablePath = @"C:\Program Files\Google\Chrome\Application\chrome.exe", // Update this path
                Args = new[] { "--no-sandbox", "--disable-gpu" },
                DefaultViewport = null
            };

            _extra = new PuppeteerExtra();
            _extra.Use(new StealthPlugin());
            _logger = logger;
        }

        public async Task LaunchBrowser()
        {
            // Launch browser
            _browser = await _extra.LaunchAsync(_options);
        }

        public async Task<bool> LoginPortal(string landingUrl)
        {
            _page = await _browser.NewPageAsync();
            try
            {
                // Navigate to the login page
                await _page.GoToAsync(landingUrl, WaitUntilNavigation.Networkidle2); // Replace with your URL

                // Wait for the button to appear
                var loginXPath = "//a[@role='button' and normalize-space(text())='Đăng nhập']";

                await _page.WaitForXPathAsync(loginXPath, new WaitForSelectorOptions { Visible = true });

                await _page.WaitForTimeoutAsync(3000);

                _logger.LogDebug("_page loaded. Trigger login button");

                // Get the element and click it
                var elements = await _page.XPathAsync(loginXPath);
                if (elements.Length > 0)
                {
                    await elements[0].ClickAsync();
                }

                await _page.WaitForTimeoutAsync(3000);

                // Trigger login by VNeID
                var loginByVneIdXpath = "//div[@id='icon-2' and @title='Tài khoản Định danh điện tử cấp bởi Bộ Công an dành cho Công dân']";
                var loginElements = await _page.XPathAsync(loginByVneIdXpath);
                if (loginElements.Length > 0)
                {
                    await loginElements[0].ClickAsync();
                }

                _logger.LogDebug("Scan QR for login, timeout is 5 minutes");
                
                await _page.WaitForTimeoutAsync(3000);
                
                var loginResult = await WaitForLogin();

                _logger.LogDebug($"Login result: {loginResult}");

                return loginResult;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error: {ex.Message}");
            }
            return false;
        }

        public async Task<bool> ThongBaoLuuTru(string landingUrl)
        {
            try
            {
                await _page.GoToAsync(landingUrl, WaitUntilNavigation.Networkidle2);

                await _page.WaitForTimeoutAsync(2000);

                _logger.LogDebug("Form loaded. Start form input");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in ThongBaoLuuTru: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> WaitForLogin()
        {
            var timeout = TimeSpan.FromMinutes(5);
            var cts = new CancellationTokenSource(timeout);

            // Create multiple detection tasks
            var tasks = new[]
            {
                WaitForUrlChangeTask(cts.Token),
            };

            try
            {
                // Wait for any of the detection methods to succeed
                await Task.WhenAny(tasks);
                return true;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
        }

        private async Task WaitForUrlChangeTask(CancellationToken cancellationToken)
        {
            var initialUrl = _page.Url;

            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);

                if (_page.Url != initialUrl)
                {
                    _logger.LogDebug($"URL changed to: {_page.Url}");
                    return;
                }
            }
        }
    }
}
