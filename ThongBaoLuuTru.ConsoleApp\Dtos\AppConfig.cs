namespace ThongBaoLuuTru.ConsoleApp.Dtos
{
    public class AppConfig
    {
        public string AppName { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public Settings Settings { get; set; } = new();
        public Urls Urls { get; set; } = new();
        public Credentials Credentials { get; set; } = new();
    }

    public class Settings
    {
        public int Timeout { get; set; }
        public int RetryCount { get; set; }
        public bool EnableLogging { get; set; }
    }

    public class Urls
    {
        public string LandingUrl { get; set; } = string.Empty;
        public string FormUrl { get; set; } = string.Empty;
    }

    public class Credentials
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }
}
